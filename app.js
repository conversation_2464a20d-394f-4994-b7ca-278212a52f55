const express = require('express');
const { Pool } = require('pg');

const app = express();
port = 8000;
app.use(express.json());
app.use(express.urlencoded({ extended: true })); // For form-data


const pool = new Pool({
    user: 'postgres',
    host: 'localhost',
    database: 'hms2025',
    password: 'Ankit@9795',
    port: 5432,
});

// Test database connection
pool.connect((err, client, release) => {
    if (err) {
        console.error('Database connection error:', err.message);
    } else {
        console.log('Database connected successfully');
        release();
    }
});





// 👉 Add Student
app.post('/students', async (req, res) => {
  console.log('Request body:', req.body);
  console.log('Request headers:', req.headers);
  const { name, age } = req.body;

  // Temporary: Just return success without database
  if (name && age) {
    res.json({ message: 'Student added', name, age });
  } else {
    res.status(400).json({ error: 'Name and age are required' });
  }

  // try {
  //   await pool.query('CALL add_student($1, $2)', [name, age]);
  //   res.send('Student added');
  // } catch (err) {
  //   res.status(500).send(err.message);
  // }
});

// 👉 Get All Students
app.get('/students', async (req, res) => {
  try {
    const result = await pool.query('SELECT * FROM get_students()');
    res.json(result.rows);
  } catch (err) {
    res.status(500).send(err.message);
  }
});

// 👉 Update Student
app.put('/students/:id', async (req, res) => {
  const { name, age } = req.body;
  const { id } = req.params;
  try {
    await pool.query('CALL update_student($1, $2, $3)', [id, name, age]);
    res.send('Student updated');
  } catch (err) {
    res.status(500).send(err.message);
  }
});

// 👉 Delete Student
app.delete('/students/:id', async (req, res) => {
  const { id } = req.params;
  try {
    await pool.query('CALL delete_student($1)', [id]);
    res.send('Student deleted');
  } catch (err) {
    res.status(500).send(err.message);
  }
});

app.listen(port, () => {
    console.log(`Server started on port ${port}`);
});
